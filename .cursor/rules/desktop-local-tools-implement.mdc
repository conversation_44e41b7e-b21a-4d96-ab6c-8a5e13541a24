---
description:
globs:
alwaysApply: false
---
**新增桌面端工具流程:**

1.  **定义工具接口 (Manifest):**
    *   **文件:** `src/tools/[tool_category]/index.ts` (例如: `src/tools/local-files/index.ts`)
    *   **操作:**
        *   在 `ApiName` 对象（例如 `LocalFilesApiName`）中添加一个新的、唯一的 API 名称。
        *   在 `Manifest` 对象（例如 `LocalFilesManifest`）的 `api` 数组中，新增一个对象来定义新工具的接口。
        *   **关键字段:**
            *   `name`: 使用上一步定义的 API 名称。
            *   `description`: 清晰描述工具的功能，供 Agent 理解和向用户展示。
            *   `parameters`: 使用 JSON Schema 定义工具所需的输入参数。
                *   `type`: 通常是 'object'。
                *   `properties`: 定义每个参数的名称、`description`、`type` (string, number, boolean, array, etc.)，使用英文。
                *   `required`: 一个字符串数组，列出必须提供的参数名称。

2.  **定义相关类型:**
    *   **文件 1:** `packages/electron-client-ipc/src/types.ts` (或类似的共享 IPC 类型文件)
        *   **操作:** 定义传递给 IPC 事件的参数类型接口 (例如: `RenameLocalFileParams`, `MoveLocalFileParams`)。确保与 Manifest 中定义的 `parameters` 一致。
    *   **文件 2:** `src/tools/[tool_category]/type.ts` (例如: `src/tools/local-files/type.ts`)
        *   **操作:** 定义此工具执行后，存储在前端 Zustand Store 中的状态类型接口 (例如: `LocalRenameFileState`, `LocalMoveFileState`)。这通常包含操作结果（成功/失败）、错误信息以及相关数据（如旧路径、新路径等）。

3.  **实现前端状态管理 (Store Action):**
    *   **文件:** `src/store/chat/slices/builtinTool/actions/[tool_category].ts` (例如: `src/store/chat/slices/builtinTool/actions/localFile.ts`)
    *   **操作:**
        *   导入在步骤 2 中定义的 IPC 参数类型和状态类型。
        *   在 Action 接口 (例如: `LocalFileAction`) 中添加新 Action 的方法签名，使用对应的 IPC 参数类型。
        *   在 `createSlice` (例如: `localFileSlice`) 中实现该 Action 方法：
            *   接收 `id` (消息 ID) 和 `params` (符合 IPC 参数类型)。
            *   设置加载状态 (`toggleLocalFileLoading(id, true)`)。
            *   调用对应的 `Service` 层方法 (见步骤 4)，传递 `params`。
            *   使用 `try...catch` 处理 `Service` 调用可能发生的错误。
            *   **成功时:**
                *   调用 `updatePluginState(id, {...})` 更新插件状态，使用步骤 2 中定义的状态类型。
                *   调用 `internal_updateMessageContent(id, JSON.stringify({...}))` 更新消息内容，通常包含成功确认信息。
            *   **失败时:**
                *   记录错误 (`console.error`)。
                *   调用 `updatePluginState(id, {...})` 更新插件状态，包含错误信息。
                *   调用 `internal_updateMessagePluginError(id, {...})` 设置消息的错误状态。
                *   调用 `internal_updateMessageContent(id, JSON.stringify({...}))` 更新消息内容，包含错误信息。
            *   在 `finally` 块中取消加载状态 (`toggleLocalFileLoading(id, false)`)。
            *   返回操作是否成功 (`boolean`)。

4.  **实现 Service 层 (调用 IPC):**
    *   **文件:** `src/services/electron/[tool_category]Service.ts` (例如: `src/services/electron/localFileService.ts`)
    *   **操作:**
        *   导入在步骤 2 中定义的 IPC 参数类型。
        *   添加一个新的 `async` 方法，方法名通常与 Action 名称对应 (例如: `renameLocalFile`)。
        *   方法接收 `params` (符合 IPC 参数类型)。
        *   使用从 `@lobechat/electron-client-ipc` 导入的 `dispatch` (或 `invoke`) 函数，调用与 Manifest 中 `name` 字段匹配的 IPC 事件名称，并将 `params` 传递过去。
        *   定义方法的返回类型，通常是 `Promise<{ success: boolean; error?: string }>`，与后端 Controller 返回的结构一致。

5.  **实现后端逻辑 (Controller / IPC Handler):**
    *   **文件:** `apps/desktop/src/main/controllers/[ToolName]Ctr.ts` (例如: `apps/desktop/src/main/controllers/LocalFileCtr.ts`)
    *   **操作:**
        *   导入 Node.js 相关模块 (`fs`, `path` 等) 和 IPC 相关依赖 (`ipcClientEvent`, 参数类型等)。
        *   添加一个新的 `async` 方法，方法名通常以 `handle` 开头 (例如: `handleRenameFile`)。
        *   使用 `@ipcClientEvent('yourApiName')` 装饰器将此方法注册为对应 IPC 事件的处理器，确保 `'yourApiName'` 与 Manifest 中的 `name` 和 Service 层调用的事件名称一致。
        *   方法的参数应解构自 Service 层传递过来的对象，类型与步骤 2 中定义的 IPC 参数类型匹配。
        *   实现核心业务逻辑：
            *   进行必要的输入验证。
            *   执行文件系统操作或其他后端任务 (例如: `fs.promises.rename`)。
            *   使用 `try...catch` 捕获执行过程中的错误。
            *   处理特定错误码 (`error.code`) 以提供更友好的错误消息。
        *   返回一个包含 `success` (boolean) 和可选 `error` (string) 字段的对象。

6.  **更新 Agent 文档 (System Role):**
    *   **文件:** `src/tools/[tool_category]/systemRole.ts` (例如: `src/tools/local-files/systemRole.ts`)
    *   **操作:**
        *   在 `<core_capabilities>` 部分添加新工具的简要描述。
        *   如果需要，更新 `<workflow>`。
        *   在 `<tool_usage_guidelines>` 部分为新工具添加详细的使用说明，解释其参数、用途和预期行为。
        *   如有必要，更新 `<security_considerations>`。
        *   如有必要（例如工具返回了新的数据结构或路径），更新 `<response_format>` 中的示例。

通过遵循这些步骤，可以系统地将新的桌面端工具集成到 LobeChat 的插件系统中。
