{"permissions": {"allow": ["Bash(ls:*)", "Bash(git add:*)", "Bash(git commit:*)", "Bash(grep:*)", "Bash(git push:*)", "Bash(find:*)", "<PERSON><PERSON>(echo:*)", "<PERSON><PERSON>(curl:*)", "Bash(rm:*)", "Bash(npm run type-check:*)", "Bash(npm run lint:*)", "Bash(ast-grep:*)", "Bash(rg:*)", "Bash(npm run typecheck:*)", "Bash(pnpm dev:*)", "Bash(npm run build:*)", "Bash(-H \"Content-Type: application/json\" )", "Bash(-w \"\\n状态码: %{http_code}\\n\")", "Bash(npx tsc:*)", "<PERSON><PERSON>(tail:*)", "mcp__context7__resolve-library-id", "mcp__context7__get-library-docs", "WebFetch(domain:platform.openai.com)", "Bash(npm run dev:*)", "Bash(--header 'lobe-auth-dev-backend-api: 1' )", "Bash(--header 'Content-Type: application/json')", "Bash(--header 'lobe-auth-dev-backend-api: 1' )", "Bash(--header 'lobe-auth-dev-backend-api: 1' )", "Bash(--header 'lobe-auth-dev-backend-api: 1' )", "Bash(--header 'lobe-auth-dev-backend-api: 1' )", "Bash(--header 'lobe-auth-dev-backend-api: 1' )", "Bash(--header 'lobe-auth-dev-backend-api: 1' )", "Bash(--header 'lobe-auth-dev-backend-api: 1' )", "Bash(--header 'lobe-auth-dev-backend-api: 1' )", "Bash(--header 'lobe-auth-dev-backend-api: 1')", "Bash(git diff:*)"], "deny": []}}