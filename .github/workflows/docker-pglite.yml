name: Publish Docker Pglite Image

on:
  workflow_dispatch:
  release:
    types: [published]
  pull_request:
    types: [synchronize, labeled, unlabeled]

concurrency:
  group: ${{ github.ref }}-${{ github.workflow }}
  cancel-in-progress: true

env:
  REGISTRY_IMAGE: lobehub/lobe-chat-pglite
  PR_TAG_PREFIX: pr-

jobs:
  build:
    # 添加 PR label 触发条件
    if: |
      (github.event_name == 'pull_request' &&
       contains(github.event.pull_request.labels.*.name, 'Build Docker')) ||
      github.event_name != 'pull_request'

    strategy:
      matrix:
        include:
          - platform: linux/amd64
            os: ubuntu-latest
          - platform: linux/arm64
            os: ubuntu-24.04-arm
    runs-on: ${{ matrix.os }}
    name: Build ${{ matrix.platform }} Image
    steps:
      - name: Prepare
        run: |
          platform=${{ matrix.platform }}
          echo "PLATFORM_PAIR=${platform//\//-}" >> $GITHUB_ENV

      - name: Checkout base
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      # 为 PR 生成特殊的 tag
      - name: Generate PR metadata
        if: github.event_name == 'pull_request'
        id: pr_meta
        run: |
          branch_name="${{ github.head_ref }}"
          sanitized_branch=$(echo "${branch_name}" | sed -E 's/[^a-zA-Z0-9_.-]+/-/g')
          echo "pr_tag=${sanitized_branch}-$(git rev-parse --short HEAD)" >> $GITHUB_OUTPUT

      - name: Docker meta
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.REGISTRY_IMAGE }}
          tags: |
            # PR 构建使用特殊的 tag
            type=raw,value=${{ env.PR_TAG_PREFIX }}${{ steps.pr_meta.outputs.pr_tag }},enable=${{ github.event_name == 'pull_request' }}
            # release 构建使用版本号
            type=semver,pattern={{version}},enable=${{ github.event_name != 'pull_request' }}
            type=raw,value=latest,enable=${{ github.event_name != 'pull_request' }}

      - name: Docker login
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKER_REGISTRY_USER }}
          password: ${{ secrets.DOCKER_REGISTRY_PASSWORD }}

      - name: Get commit SHA
        if: github.ref == 'refs/heads/main'
        id: vars
        run: echo "sha_short=$(git rev-parse --short HEAD)" >> $GITHUB_OUTPUT

      - name: Build and export
        id: build
        uses: docker/build-push-action@v5
        with:
          platforms: ${{ matrix.platform }}
          context: .
          file: ./Dockerfile.pglite
          labels: ${{ steps.meta.outputs.labels }}
          build-args: |
            SHA=${{ steps.vars.outputs.sha_short }}
          outputs: type=image,name=${{ env.REGISTRY_IMAGE }},push-by-digest=true,name-canonical=true,push=true

      - name: Export digest
        run: |
          rm -rf /tmp/digests
          mkdir -p /tmp/digests
          digest="${{ steps.build.outputs.digest }}"
          touch "/tmp/digests/${digest#sha256:}"

      - name: Upload artifact
        uses: actions/upload-artifact@v4
        with:
          name: digest-${{ env.PLATFORM_PAIR }}
          path: /tmp/digests/*
          if-no-files-found: error
          retention-days: 1

  merge:
    name: Merge
    needs: build
    runs-on: ubuntu-latest
    steps:
      - name: Checkout base
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Download digests
        uses: actions/download-artifact@v4
        with:
          path: /tmp/digests
          pattern: digest-*
          merge-multiple: true

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      # 为 merge job 添加 PR metadata 生成
      - name: Generate PR metadata
        if: github.event_name == 'pull_request'
        id: pr_meta
        run: |
          branch_name="${{ github.head_ref }}"
          sanitized_branch=$(echo "${branch_name}" | sed -E 's/[^a-zA-Z0-9_.-]+/-/g')
          echo "pr_tag=${sanitized_branch}-$(git rev-parse --short HEAD)" >> $GITHUB_OUTPUT

      - name: Docker meta
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.REGISTRY_IMAGE }}
          tags: |
            type=raw,value=${{ env.PR_TAG_PREFIX }}${{ steps.pr_meta.outputs.pr_tag }},enable=${{ github.event_name == 'pull_request' }}
            type=semver,pattern={{version}},enable=${{ github.event_name != 'pull_request' }}
            type=raw,value=latest,enable=${{ github.event_name != 'pull_request' }}

      - name: Docker login
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKER_REGISTRY_USER }}
          password: ${{ secrets.DOCKER_REGISTRY_PASSWORD }}

      - name: Create manifest list and push
        working-directory: /tmp/digests
        run: |
          docker buildx imagetools create $(jq -cr '.tags | map("-t " + .) | join(" ")' <<< "$DOCKER_METADATA_OUTPUT_JSON") \
            $(printf '${{ env.REGISTRY_IMAGE }}@sha256:%s ' *)

      - name: Inspect image
        run: |
          docker buildx imagetools inspect ${{ env.REGISTRY_IMAGE }}:${{ steps.meta.outputs.version }}
